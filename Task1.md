The Task : Add notice to USPS extension about new API integration
@sam.na<PERSON><PERSON> said:

@laura.johnson what do you think about adding a notice to USPS extension ( and the banner that instructs on how they have to create an API key ) explaining that we’re working on the new API integration? this will ensure merchants don’t frustrated when they receive email telling them the old API is getting deprecated.

@laura.johnson can you please provide the right copy for this?

DoD

Add notice about this migration for existing installs.

Add notice about the migration on the initial instructions banner.



==========

The Full Context :
Git hub issue :
A chatter in 7911819-zen is a new customer and tried to follow the documentation to get the API key and got this message back:

    To sign up as a new USPS API customer, please go to https://developer.usps.com/ and follow the Getting Started instructions. If you are using a third-party software provider and have been asked to obtain a Web Tools UserID, please reply and let us know the name of the software.

    Once Signed in, the APIs should be self-service and there is also a contact email (in the Getting Started Instructions and API Catalog) to the team that manages those APIs if you need any further assistance. We are planning on retiring Web Tools in 2024 and are only approving Web Tools API access on a limited basis. Please use the new USPS API platform unless you are using a third-party software provider which necessitates a Web Tools USERID, in which case, let us know the name of the third-party software provider.

It seems like this is going to be the messaging for all new customers. Reporting since I don't see any indication of preparing for the new method of using an API key


Thanks for reporting this @DustinHartzler!
We've been keeping an eye on their Web Tools API updates/release notes and hadn't noticed any deprecation messaging. In September we had assessed the effort required to integrate the new REST API in this issue. However, as there was no announced deprecation date we didn't prioritize the integration.

on Jul 16, 2024

I just had a user reach out in 8476226-zen with the following:

    USPS industry alerts have all indicated that on July 14th, Webtools labels APIs are being retired and will no longer work. No new logins are being granted.

The official email reads:

    Webtools Label Migration Dates

     

     

     

     

     

    On July 14, 2024, the Webtools Label API is retiring.  All users must migrate to the USPS Labels APIs.

     

    Contact your USPS account representative for assistance in migrating to the USPS Labels API v3. They can provide you with aMigration Feedback Form to facilitate the migration to the modernized API label solution.

     

    Migration Deadline Details for Label APIs:

    Integrators using the Web Tools Domestic, International, Returns Label APIs, and SCAN Form API should migrate to the new USPS Domestic and International Label APIs by July 14, 2024. The new USPS Label APIs (https://developer.usps.com) offer more API customization, product offerings, and payment options. Additional features include increased security via OAuth 2.0 authentication and webhooks push notifications.



Web Tools Label APIs 	USPS Label APIs
eVS (Domestic) 	Domestic Label API (Domestic Labels 3.0)
eVSCancel 	Domestic Label API (Domestic Labels 3.0)
eVSExpressMailIntl 	International Label API (International Labels 3.0)
eVSPriorityMailIntl 	International Label API (International Labels 3.0)
eVSFirstClassMailIntl 	International Label API (International Labels 3.0)
eVSICancel 	International Label API (International Labels 3.0)
SCAN 	SCAN Form API (SCAN Form 3.0)
USPSReturnsLabel 	Domestic Label API (Domestic Labels 3.0)



Additional Support for Migration:

Additional support for Label API migration and Mapping can be found under Announcements at Web Tools APIs | USPS.  ContactAPISupport@usps.<NAME_EMAIL> for additional support.
Webtools Label Migration Dates

On July 14, 2024, the Webtools Label API is retiring. All users must migrate to the USPS Labels APIs.

Contact your USPS account representative for assistance in migrating to the USPS Labels API v3. They can provide you with a Migration Feedback Form to facilitate the migration to the modernized API label solution.

Migration Deadline Details for Label APIs:
Integrators using the Web Tools Domestic, International, Returns Label APIs, and SCAN Form API should migrate to the new USPS Domestic and International Label APIs by July 14, 2024. The new USPS Label APIs (https://developer.usps.com) offer more API customization, product offerings, and payment options. Additional features include increased security via OAuth 2.0 authentication and webhooks push notifications.

Web Tools Label APIs
USPS Label APIs
eVS (Domestic)
Domestic Label API (Domestic Labels 3.0)
eVSCancel
Domestic Label API (Domestic Labels 3.0)
eVSExpressMailIntl
International Label API (International Labels 3.0)
eVSPriorityMailIntl
International Label API (International Labels 3.0)
eVSFirstClassMailIntl
International Label API (International Labels 3.0)
eVSICancel
International Label API (International Labels 3.0)
SCAN
SCAN Form API (SCAN Form 3.0)
USPSReturnsLabel
Domestic Label API (Domestic Labels 3.0)

Additional Support for Migration:
Additional support for Label API migration and Mapping can be found under Announcements at Web Tools APIs | USPS. Contact APISupport@usps.<NAME_EMAIL> for additional support.

Thanks!

On 8583830-zen, the merchant attempted to request API access <NAME_EMAIL> directly (which seems to be the wrong contact email, based on the reply) and they received the following response:

    Please utilize the new USPS API Platform at the Developer Portal (Note: the new platform is separate from Web Tools and Web Tools credentials and requests will not work with the new APIs). To sign up as a new USPS API customer, please go to https://developer.usps.com/ and follow the Getting Started instructions. Once Signed in, the APIs should be self-service and there is also a contact email (in the Getting Started Instructions and API Catalog) to the team that manages APIs if you need any further assistance. We are planning on retiring Web Tools in 2026 and are only approving Web Tools API access on a limited basis.
    If you are utilizing a third-party software/solution that necessitates Web Tools API Access, please reference the instructions in the Web Tools Registration Email.

This response looks quite similar to the email Dustin shared when he created this issue, with the main (important) difference being this sentence:

    We are planning on retiring Web Tools in 2026 and are only approving Web Tools API access on a limited basis.

The retirement date appears to have been pushed back 2 years (from 2024 to 2026) but the limited approval of Web Tools API access is a bit concerning.


dustinparker
on Aug 19, 2024

@shaunkuschel I searched through the web tools API site, documentation, release notes, etc.. and only found the following statement:

usps-web-tools

There is no indication I can find that users can't still apply for legacy API access. Do we have a connection at USPS that we could reach out to to confirm if/when API access will be restricted?

shaunkuschel
on Aug 20, 2024

Interestingly, the USPS Web Tools team beat us to it and reached out to us directly in a separate ticket of their own (8604562-zen), saying:

    Message: Hello,
    This is a message from USPS Web Tools. We receive many requests from customers wishing to utilize Web Tools APIs through their WooCommerce plugins. We find that the instructions these customers are receiving are incomplete. For example, customers often request access without even providing a Web Tools USERID or knowing how to receive one. Customers also request access to "the Web Tools API". There are many Web Tools APIs, so this is not very helpful. We have very specific instructions in our Web Tools Registration Email and would ask that customers utilize these instructions in their entirety. We would also ask that customers only request access to the APIs that they actually need access to (typically only the Rate Calculator APIs). If customers do need access to other APIs, they should request them specifically. There are specific instructions for obtaining Tracking and Label API access in the Web Tools Registration Email, so customers should utilize those instructions and not request <NAME_EMAIL>. Please let us know if you have any questions.
    Contact name: USPS Web Tools
    Contact email: <EMAIL>

Our AI Assistant sent an immediate reply to that message, which as was actually pretty good:

Thank you for your message and for providing additional clarity on the process for obtaining access to USPS Web Tools APIs. We appreciate your feedback and will ensure that our instructions are as clear and complete as possible for our users.

To our users, here are the updated steps to get started with the USPS Shipping Method extension:

1. Register for a USPS Web Tools API Account:

- Go to the [USPS.com Web Tools Portal](https://www.usps.com/business/web-tools-apis/welcome.htm) to register for a free account.
- You will receive a welcome email from USPS with your new USPS User ID (note that this is not the same as the username used to sign in to USPS.com).

2. Request API Access and Permissions:

- Follow the specific instructions provided in the USPS Web Tools Registration Email to request access to the necessary APIs.
- Typically, you will need access to the Rate Calculator APIs. If you need access to other APIs (e.g., Tracking and Label APIs), follow the specific instructions in the registration email for those APIs.
- If you need to contact USPS support, email [<EMAIL>](mailto:<EMAIL>) and include your USPS User ID and the specific APIs you need access to.

3. Configure the USPS Shipping Method in WooCommerce:

- Once your account has API access, go to WooCommerce > Settings > Shipping > USPS on your site.
- Insert your USPS User ID and configure the settings to start using live rates.

The Web Tools team followed up with a couple edits:

    Thank you for the response. A couple of things.

        You can even link directly to the Web Tools Registration Page as customers may sign up for the new Platform if they follow your link.
        If necessary, you can provide a sample (edit as needed):

Web Tools USER ID:
USPS Mailer ID: I am a WooCommerce merchant and do not have my own Mailer ID (or they can provide their Mailer ID if they have one)
Company Website:
List the API(s) for which your are requesting access: The Rate Calculator APIs (RateV4 and IntlRateV2).
Expected volume:
Are you a shipper or software provider? Please provide details on how your business will use the APIs: Shipper. Our business will be using the Web Tools Rate Calculator APIs to display USPS rates at checkout to customers in our ecommerce shop.
Third-party software/software provider: WooCommerce

We updated that section of our USPS Shipping Method docs to include that info as requested.

However, the Web Tools team is now asking the following:

    Can you also clarify if WooCommerce is actively working to utilize the new USPS API Platform at the Developer Portal?

I assume the answer to that question is no, we're not actively working to utilize the new USPS API Platform. Is that correct?

shaunkuschel
on Aug 20, 2024

@dustinparker - The USPS Web Tools team replied with the following information about the API retirement dates:

    We are already in the process of retiring the Web Tools APIs, beginning with the Label APIs. We have sent out communications and have been transitioning customers using the Label APIs (eVS, eVS International, Returns and SCAN) to the new Platform. Below is a snippet from the communications we have sent regarding the changes:

    Important Dates:

        Migration Deadline for Web Tools Label APIs: July 14th, 2024
        Retirement Date of all Web Tools APIs: January 25th, 2026

(Note: The above email/info is from the USPS Web Tools team directly, on 8604562-zen)

dustinparker
on Aug 21, 2024

Hi @shaunkuschel, thanks for posting all of that information! Yes, we are already working on integrating the new USPS API.

paulostp
on Aug 23, 2024

Just noting that the conversation with the USPS team in 8604562-zen has reached a stopping point, but they have asked that we share any future updates with them. I'm sure they are also available to answer any question we may have about the new API, so we can re-open the ticket or create a follow-up at any point, if needed.

bartech
on Oct 21, 2024
TL;DR

I had a email thread with USPS support.

    We still experiencing 400 error when performing request (https://api.usps.com/shipments/v3/options/search - endpoint) that is correct according to documentation and USPS support.
    This endpoint was working for us.
    Support can't recreate the issue with given request.
    Confirmed our API key have correct permissions.
    The USPS Web Tools will be retiring in January 2026.

Hello,

Is there an issue with the Shipping Options API?
https://api.usps.com/shipments/v3/options/search
https://api-cat.usps.com/shipments/v3/options/search

both return

{
"apiVersion": "/shipments/v3",
"error": {
"code": "400",
"message": "The request body is invalid or malformed.",
"errors": []
}
}

My request looks correct. I tested all possible options, including fields not required according to documentation. When there is an issue, the API returns an exact error. In this case errors are empty.

Example request

{
"originZIPCode": "33165",
"destinationZIPCode": "33033",
"packageDescription": {
"weight": 1,
"length": 12,
"width": 5,
"height": 5,
"mailClass": "ALL"
}
}

Please let me know if I'm doing something wrong, is documentation not updated or maybe there is a bug in the newest release.

Regards,
Bartosz

Hello,

You are experiencing this error due to not having all required fields. According to documentation:
Image

Please include this section in your request.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

Hello Samantha,

Thank you for the fast response. I indeed tried all fields before I wrote the previous email. I tried to include even none required and every time I got this error.
I tried all combinations of those fields to include them one by one, to use ZIP in format XXXXX and XXXXX-XXXX, different package sizes etc.
Image

If I make an error on purpose, for example I misspell one of the enums. I'll get the exact error description.
Image

But when the request seemed to be OK, I got this generic message "The request body is invalid or malformed."
I got this issue on both Live and Developer environments.

Also it's not only me, as my colleague also tried the request from different software (POSTMAN) and got the same issue.

Thank you,
Bartosz

Hello,

Can you please send me the full raw updated request you are using?

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

{
"pricingOptions": [{
"priceType": "COMMERCIAL"
}],
"originZIPCode": "33165",
"destinationZIPCode": "33033",
"packageDescription": {
"weight": 1,
"length": 12,
"width": 5,
"height": 5,
"mailClass": "ALL"
}
}

Hello,

What endpoint are you sending this to? What is the exact error you are getting?

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

https://api-cat.usps.com/shipments/v3/options/search

{
"apiVersion": "/shipments/v3",
"error": {
"code": "400",
"message": "The request body is invalid or malformed.",
"errors": []
}
}

same error is for Live API endpoint
https://api.usps.com/shipments/v3/options/search

Can you reproduce the issue?

Also if service was down I expect it return 503 error.

Hello,

I am not able to get A 400 response to return with the request below.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

Super wired.

I can get 200 from Domestic and International API
https://api-cat.usps.com/prices/v3/total-rates/search
https://api-cat.usps.com/nternational-prices/v3/total-rates/search

Image

Only shipments/v3/options/search is giving me 400.
If this was some weird header value or some bug in encoding I expect this will affect all endpoints.
And as said, not only me is getting this error, other dev in another country, so not same ISP, is also affected.

Hello,

Currently, as I have previously stated, the Shipping Options 3.0 API is experiencing issues in the CAT environment. This could explain why you are having issues. My development team is aware of these issues.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

Can't be only CAT as I'm getting exact same error on Prod https://api.usps.com/shipments/v3/options/search

Hello,

Using the exact request you provide in another email, I received a successful response in Postman.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

Maybe it's related to my API key.

Can you please compare with your response from /oauth2/v3/token
am I missing some scope, although I see shipments in it.
Image

Hello,

You are correct, “shipments” is the Shipping Options 3.0 API. Based on your scope, you should be all set.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260

BTW I'm implementing the REST API on the official WooCommerce USPS extension https://woocommerce.com/products/usps-shipping-method/.
That's at least 5000 users which are using this integration. Do you know when SOAP Domestic and International Prices will be shut down?
We have to switch before that time and right now I'm blocked, so quite important.

Also thanks for your time, appreciate quick turnaround with the responses.
Thank you Samantha!

Hello,

The USPS Web Tools will be retiring in January 2026.

Thank you,

Samantha | USPS API Support
<EMAIL>
475 L’Enfant Plaza SW
Washington, DC 20260
