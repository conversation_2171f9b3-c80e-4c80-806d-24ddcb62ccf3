/* global usps_migration_notice */
(function ($) {
    'use strict';

    // Handle migration notice dismissal.
    $(document).on('click', '#wc-usps-migration-notice .notice-dismiss', function (e) {
        if (!usps_migration_notice.ajaxurl || !usps_migration_notice.nonce) {
            return;
        }

        $.ajax({
            url: usps_migration_notice.ajaxurl,
            type: 'POST',
            data: {
                action: 'wc_usps_dismiss_migration_notice',
                nonce: usps_migration_notice.nonce
            },
            success: function (response) {
                // Notice will be hidden automatically by WordPress
            },
            error: function (xhr, status, error) {
                console.error('Failed to dismiss USPS migration notice:', error);
            }
        });
    });

})(jQuery);
