# USPS Migration Notice - QA Test Plan

## Overview
This QA plan covers the implementation of USPS API migration notices for the WooCommerce USPS Shipping plugin. The implementation includes both general admin notices and enhanced API setup instructions.

## Features Implemented

### 1. Migration Notice Infrastructure
- **Location**: `includes/class-wc-usps.php`
- **Methods Added**:
  - `migration_notice()` - Displays the migration notice using `wp_admin_notice()`
  - `dismiss_migration_notice()` - Handles AJAX dismissal
- **Hooks Added**:
  - `admin_notices` for displaying notices
  - `wp_ajax_wc_usps_dismiss_migration_notice` for dismissal handling
- **WordPress Core Functions Used**:
  - `wp_admin_notice()` for modern WordPress notice display
  - `wp_create_nonce()` for security
  - `update_user_meta()` for dismissal state storage

### 2. Enhanced API Setup Instructions
- **Location**: `includes/class-wc-shipping-usps.php`
- **Fields Updated**:
  - `user_id` field description (SOAP API)
  - `client_id` field description (REST API)
  - `client_secret` field description (REST API)

### 3. JavaScript Functionality
- **Location**: `assets/js/migration-notice.js` (separate file)
- **Features**:
  - Dismissible notice handling with AJAX
  - Error handling and logging
  - Uses dedicated localized script object `usps_migration_notice`
  - Isolated from existing admin.js functionality

### 4. CSS Styling
- **Approach**: Uses WordPress core `wp_admin_notice()` styling
- **No Custom CSS**: Relies on WordPress default admin notice styles
- **Benefits**: Consistent with WordPress admin interface, automatic accessibility

## Test Scenarios

### A. Migration Notice Display Tests

#### A1. Notice Visibility Conditions
**Test**: Verify notice appears only for appropriate users and conditions
- [ ] **User Capability**: Notice only shows for users with `manage_woocommerce` capability
- [ ] **USPS Configuration**: Notice only shows when USPS shipping method instances exist
- [ ] **Screen Context**: Notice appears on Dashboard, Plugins, and WooCommerce Settings pages
- [ ] **Dismissal State**: Notice doesn't appear if user has dismissed it

#### A2. Notice Content Verification
**Test**: Verify notice content and links
- [ ] **Title**: "USPS API Update Coming Soon" displays correctly
- [ ] **Message**: Migration explanation is clear and informative
- [ ] **Links**: All three links work correctly:
  - Review Settings → USPS settings page
  - Documentation → Plugin documentation
  - Get Support → Support page
- [ ] **Styling**: Notice uses proper WordPress info notice styling via `wp_admin_notice()`
- [ ] **WordPress Standards**: Notice follows WordPress core notice patterns

#### A3. Notice Dismissal Functionality
**Test**: Verify dismissal works correctly
- [ ] **Dismiss Button**: WordPress dismiss button appears and is functional
- [ ] **AJAX Request**: Dismissal sends proper AJAX request with nonce
- [ ] **User Meta**: Dismissal state stored in user meta correctly
- [ ] **Persistence**: Notice doesn't reappear after dismissal
- [ ] **Security**: Nonce verification works properly
- [ ] **Error Handling**: Failed dismissal doesn't break functionality

### B. Enhanced API Setup Instructions Tests

#### B1. SOAP API (Legacy) Instructions
**Test**: Verify enhanced user_id field description
- [ ] **Original Content**: Original setup instructions remain intact
- [ ] **Migration Context**: Migration note appears in description
- [ ] **Links**: USPS registration and documentation links work
- [ ] **Formatting**: HTML formatting displays correctly

#### B2. REST API Instructions
**Test**: Verify enhanced REST API field descriptions
- [ ] **Client ID Field**: Migration context added to description
- [ ] **Client Secret Field**: Migration context added to description
- [ ] **Tooltip Display**: desc_tip functionality still works
- [ ] **Field Visibility**: Fields show/hide based on API type selection

### C. JavaScript Functionality Tests

#### C1. Script Loading
**Test**: Verify JavaScript loads correctly
- [ ] **Admin Pages**: Migration notice script loads on all admin pages
- [ ] **Dependencies**: jQuery dependency resolved correctly
- [ ] **Localization**: `usps_migration_notice` object available with ajaxurl and nonce
- [ ] **Isolation**: Migration notice script separate from admin.js
- [ ] **Settings Pages**: Original admin.js still loads correctly on settings pages

#### C2. AJAX Dismissal
**Test**: Verify AJAX dismissal functionality
- [ ] **Event Binding**: Click event binds to dismiss button correctly
- [ ] **Data Extraction**: Nonce extracted from notice data attribute
- [ ] **AJAX Request**: Request sent to correct endpoint with proper data
- [ ] **Success Handling**: Success response handled gracefully
- [ ] **Error Handling**: Errors logged to console appropriately

### D. CSS Styling Tests

#### D1. Visual Appearance
**Test**: Verify notice styling
- [ ] **WordPress Standards**: Follows WordPress admin notice conventions
- [ ] **Color Scheme**: Uses appropriate info notice colors
- [ ] **Spacing**: Proper padding and margins
- [ ] **Typography**: Text is readable and properly formatted

#### D2. Responsive Design
**Test**: Verify responsive behavior
- [ ] **Desktop**: Notice displays correctly on desktop screens
- [ ] **Tablet**: Notice adapts properly to tablet screens
- [ ] **Mobile**: Notice remains functional on mobile devices

### E. Integration Tests

#### E1. WordPress Integration
**Test**: Verify WordPress core integration
- [ ] **Admin Notices**: Uses WordPress admin_notices hook correctly
- [ ] **User Meta**: Uses WordPress user meta functions
- [ ] **AJAX**: Uses WordPress AJAX system properly
- [ ] **Nonces**: Uses WordPress nonce system for security
- [ ] **Capabilities**: Uses WordPress capability system

#### E2. WooCommerce Integration
**Test**: Verify WooCommerce integration
- [ ] **Settings Pages**: Works with WooCommerce settings framework
- [ ] **Shipping Methods**: Integrates with shipping method system
- [ ] **Capability Checks**: Uses WooCommerce capabilities correctly

### F. Security Tests

#### F1. Access Control
**Test**: Verify proper access control
- [ ] **Capability Checks**: Only authorized users can see/dismiss notices
- [ ] **Nonce Verification**: All AJAX requests verify nonces
- [ ] **Data Sanitization**: User input properly sanitized
- [ ] **XSS Prevention**: Output properly escaped

#### F2. AJAX Security
**Test**: Verify AJAX endpoint security
- [ ] **Authentication**: User authentication required
- [ ] **Authorization**: User authorization checked
- [ ] **Nonce Validation**: Nonce validation prevents CSRF
- [ ] **Error Messages**: No sensitive information in error messages

## Browser Compatibility Testing

### Supported Browsers
- [ ] **Chrome** (latest 2 versions)
- [ ] **Firefox** (latest 2 versions)
- [ ] **Safari** (latest 2 versions)
- [ ] **Edge** (latest 2 versions)

### JavaScript Features
- [ ] **jQuery**: Compatible with WordPress jQuery version
- [ ] **AJAX**: XMLHttpRequest functionality works
- [ ] **Event Handling**: Click events work properly
- [ ] **Console Logging**: Error logging works in dev tools

## Performance Testing

### Page Load Impact
- [ ] **Admin Pages**: No significant impact on admin page load times
- [ ] **JavaScript**: Minimal JavaScript footprint
- [ ] **CSS**: Minimal CSS impact
- [ ] **AJAX Requests**: Dismissal requests complete quickly

### Database Impact
- [ ] **User Meta**: Minimal database impact for dismissal storage
- [ ] **Queries**: No additional database queries on page load
- [ ] **Cleanup**: No unnecessary data accumulation

## Accessibility Testing

### Screen Reader Compatibility
- [ ] **Notice Content**: Screen readers can read notice content
- [ ] **Dismiss Button**: Dismiss button accessible via keyboard
- [ ] **Links**: All links properly labeled and accessible

### Keyboard Navigation
- [ ] **Tab Order**: Proper tab order through notice elements
- [ ] **Dismiss Action**: Can dismiss notice using keyboard
- [ ] **Link Navigation**: Can navigate links using keyboard

## Regression Testing

### Existing Functionality
- [ ] **Environment Check**: Original environment check notices still work
- [ ] **Settings Pages**: USPS settings pages function normally
- [ ] **API Configuration**: API setup process unchanged
- [ ] **Shipping Calculations**: Shipping rate calculations unaffected

### Plugin Compatibility
- [ ] **WordPress Core**: Compatible with supported WordPress versions
- [ ] **WooCommerce**: Compatible with supported WooCommerce versions
- [ ] **PHP Versions**: Compatible with supported PHP versions

## Test Data Requirements

### User Accounts
- **Admin User**: User with `manage_woocommerce` capability
- **Shop Manager**: User with limited WooCommerce access
- **Regular User**: User without WooCommerce capabilities

### USPS Configuration
- **Configured Instance**: Site with USPS shipping method configured
- **Unconfigured Instance**: Site without USPS shipping method
- **Multiple Instances**: Site with multiple USPS shipping zones

### Browser Environments
- **Clean Browser**: No cached data or extensions
- **Cached Browser**: With cached plugin assets
- **Development Tools**: With browser dev tools open

## Success Criteria

### Primary Goals
- [ ] Migration notice displays appropriately to relevant users
- [ ] Notice can be dismissed and stays dismissed
- [ ] Enhanced API setup instructions provide clear migration context
- [ ] No regression in existing functionality

### Secondary Goals
- [ ] Notice follows WordPress and WooCommerce design patterns
- [ ] Implementation follows WordPress coding standards
- [ ] Performance impact is minimal
- [ ] Accessibility requirements are met

## Known Limitations

1. **Notice Scope**: Notice only appears on Dashboard, Plugins, and WooCommerce Settings pages
2. **User-Specific**: Dismissal is per-user, not site-wide
3. **Configuration Dependent**: Only shows when USPS is configured

## Post-Implementation Monitoring

### Metrics to Track
- [ ] **Dismissal Rate**: How many users dismiss the notice
- [ ] **Support Requests**: Any increase in migration-related support requests
- [ ] **Error Logs**: Any JavaScript or PHP errors related to the notice
- [ ] **Performance**: Any impact on admin page load times

### Feedback Collection
- [ ] **User Feedback**: Collect feedback on notice clarity and usefulness
- [ ] **Support Team**: Gather feedback from support team on user questions
- [ ] **Analytics**: Track user interaction with notice links
