[![CI](https://github.com/woocommerce/woocommerce-shipping-usps/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-usps/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-usps/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-usps/actions/workflows/cron_qit.yml)

woocommerce-shipping-usps
====================

Obtain shipping rates dynamically via the USPS Shipping API for your orders.

| Product Page | Documentation | Ideas board | Build Status |
| ------------ | ------------- | ----------- | ------------ |
| http://woocommerce.com/products/usps-shipping-method/ | https://docs.woocommerce.com/document/usps-shipping-method/ | https://ideas.woocommerce.com/forums/133476-woocommerce/category/75738-category-shipping-methods | [![Build Status](https://travis-ci.com/woocommerce/woocommerce-shipping-usps.svg?token=pB9vx6zyNSauMrAK15Js&branch=master)](https://travis-ci.com/woocommerce/woocommerce-shipping-usps) |

## NPM Scripts

WooCommerce Shipping USPS utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.
