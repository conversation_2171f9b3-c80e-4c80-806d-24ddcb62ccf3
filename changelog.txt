*** USPS Shipping ***

2025-08-11 - version 5.2.3
* Tweak - WooCommerce 10.1 compatibility.

2025-07-24 - version 5.2.2
* Fix   - Elementor compatibility.

2025-07-14 - version 5.2.1
* Tweak - WooCommerce 10.0 compatibility.

2025-06-17 - version 5.2.0
* Tweak - WooCommerce 9.9 compatibility.

2025-06-03 - version 5.1.9
* Replace ubuntu-20.04 with ubuntu-latest in GitHub Actions.
* Update Requires headers for WooCommerce compatibility.

2025-04-07 - version 5.1.8
* Tweak - WooCommerce 9.8 compatibility.

2025-04-01 - version 5.1.7
* Fix   - Do not process the unlisted services.

2025-03-20 - version 5.1.6
* Add   - First-Class Mail Large Envelope Presort service.

2025-03-17 - version 5.1.5
* Fix   - Pack multiple line items into same shipping package when "Weight based" Parcel Packing Method is selected.

2025-03-10 - version 5.1.4
* Fix   - Declared value option for international shipping when "Weight based" Parcel Packing Method is selected.

2025-03-04 - version 5.1.3
* Tweak - WooCommerce 9.7 Compatibility.

2025-01-27 - version 5.1.2
* Tweak - PHP 8.4 Compatibility.

2024-12-09 - version 5.1.1
* Fix - PHP 8.3 deprecation notices.
* Fix - PHP 8.1 compatibility: htmlspecialchars_decode.

2024-11-19 - version 5.1.0
* Add - New First-Class Mail postcards services.
* Tweak - Load box packer as composer package.
* Tweak - Box packer options name and description.
* Tweak - Ensure flat rate box length is always the longest side.

2024-10-28 - version 5.0.1
* Tweak - WordPress 6.7 and WooCommerce 9.3 compatibility.

2024-07-17 - version 5.0.0
* Add - Compatibility with the new WooCommerce product editor.
* Add - Log debug messages on legacy/blocks cart and checkout.
* Add - Media Mail® restrictions reminder when selecting it as a possible rate.
* Fix - Shipping date API parameter on Russian language
* Fix - Broken international rate response XML to JSON conversion.

2024-07-02 - version 4.9.1
* Tweak - WordPress 6.6 and WooCommerce 9.0 Compatibility.

2024-06-04 - version 4.9.0
* Add - Clarify that USPS User ID isn't the same as USPS.com username.
* Fix - Broken anchor tag HTML in the USPS invalid user ID notice.
* Fix - Made the default names of the USPS services match between the frontend and backend.

2024-05-27 - version 4.8.6
* Fix - Corrected deprecated null parameter to comply with updated PHP standards.

2024-05-20 - version 4.8.5
* Tweak - Change woo.com link to woocommerce.com.

2024-05-06 - version 4.8.4
* Fix - Fatal error on cart/checkout page when using weight based with empty dimensions on product.
* Fix - Flat Rate Box Weights table display flicker in USPS settings screen.

2024-03-25 - version 4.8.3
* Tweak - WordPress 6.5 Compatibility.

2024-02-14 - version 4.8.2
* Fix - Removed WC_Boxpack class_exists check as it is no longer needed.

2024-02-12 - version 4.8.1
* Fix - Apply WordPress coding standards.

2024-01-30 - version 4.8.0
* Add - Prefix Box-Packer namespacing.

2023-12-11 - version 4.7.6
* Tweak - Remove TGM Plugin Activation dependency.

2023-10-16 - version 4.7.5
* Fix - Shipping debug info not showing when using WC cart and checkout blocks.

2023-09-05 - version 4.7.4
* Update - Security updates.

2023-08-22 - version 4.7.3
* Fix - Multiple USPS shipping methods list in cart & checkout.

2023-08-08 - version 4.7.2
* Update - Security updates.

2023-07-11 - version 4.7.1
* Fix - Remove Ground Advantage "Cubic" and "Hold For Pickup" variants from supported rates.

2023-07-10 - version 4.7.0
* Add - Support for Ground Advantage™ services.

2023-02-27 - version 4.6.3
* Tweak - Remove discontinued Regional Rate Boxes (A1, A2, B1, B2).

2023-01-31 - version 4.6.2
* Fix - Fatal error on PHP 8 when aborting unpacked item.

2023-01-16 - version 4.6.1
* Add - New option for sorting the returned rates by price.

2022-10-25 - version 4.6.0
* Add - Declared HPOS Compatibility

2022-09-06 - version 4.5.1
* Fix   - Remove unnecessary files from plugin zip file.
* Tweak - WC 6.8 compatibility.

2022-08-03 - version 4.5.0
* Fix   - Change dimensions and weight units passed to WC_Boxpack.
* Tweak - Check if WooCommerce\BoxPacker\WC_Boxpack class exists before including.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.7 and WP 6.0 compatibility.

2022-05-25 - version 4.4.74
* Fix - Remove Non-Standard fees from rate total if a package matches USPS Tube dimensions.
* Fix - Fatal error "Call to a member function get_option() on null" if shipping and shipping calculations are disabled in Shipping location(s) option.

2022-05-12 - version 4.4.73
* Fix - Remove class_exists check for WC_Boxpack. Jetpack Autoloader handles this now.

2022-05-05 - version 4.4.72
* Fix - Added box-packer composer.lock file to keep library versions exact.

2022-05-03 - version 4.4.71
* Tweak - Implement new box packer class and add setting for box packer library selection.
* Tweak - Change default Box Packer Library selection to "Enhanced Packer" for new USPS plugin installs.

2022-03-29 - version 4.4.70
* Fix   - Fatal error: "Uncaught TypeError: sizeof()" thrown on PHP 8.0.15.
* Tweak - Update debug message to include product ID.
* Tweak - WC 6.3 and WP 5.9 compatibility.

2022-01-04 - version 4.4.69
* Fix - Missing Languages folder and .pot file in release-ready zip file.

2021-12-13 - version 4.4.68
* Fix - Remove flat rate board game box as it is no longer supported by the API.
* Tweak - WC 5.9 compatibility.

2021-10-26 - version 4.4.67
* Fix - Incorrect USPS User ID notice shown for sites with existing USPS method instances.

2021-10-21 - version 4.4.66
* Fix - Remove default user ID "150WOOTH2143" as it no longer available.

2021-10-20 - version 4.4.65
* Add - Require the WooCommerce plugin with TGM Plugin Activation library.

2021-09-29 - version 4.4.64
* Fix - Unpacked items returning API rates when supposed to ignore.
* Fix - Allow found rates to be filtered.

2021-09-10 - version 4.4.63
* Tweak - Show the specific rate label when only offering customers the cheapest rate.

2021-08-31 - version 4.4.62
* Fix   - Double debug notice in checkout page.
* Tweak - Move Shipping Rates (Commercial|Retail) option out of API rates section.
* Tweak - Add packed box dimensions to hidden metadata on order items.

2021-08-19 - version 4.4.61
* Add   - Add options to USPS settings for overriding default product dimensions/weights.
* Fix   - Add options for including/overriding empty flat rate box weights.

2021-08-12 - version 4.4.60
* Fix   - Add warning for users offering commercial rates with WooCommerce's USPS user ID.
* Fix   - Adjusted flat rate envelope heights to match API results.
* Tweak - Output error messages in debug when API response is WP_Error.

2021-07-27 - version 4.4.59
* Fix - \ character in box name getting escaped exponentially.

2021-07-21 - version 4.4.58
* Fix - PHP Warning when fallback amount not set.

2021-07-13 - version 4.4.57
* Fix - Change <MailType> and <SortBy> tags based on package type and dimensions.

2021-07-07 - version 4.4.56
* Add - Parcel Select Ground™ service

2021-06-29 - version 4.4.55
* Fix - Adjust domestic box shipping API request when regional rate box is packed.

2021-06-28 - version 4.4.54
* Fix - Show all priority rates unless Offer Rates setting is set to "cheapest".
* Fix - Change Padded Flat Rate Envelope (d63 and d29) heights to .5".

2021-06-22 - version 4.4.53
* Fix - Remove <SortBy> tag from domestic flat rate request.

2021-06-10 - version 4.4.52
* Fix - Single cart item dimensions were being passed to USPS instead of the packed Flat Rate Box dimensions.

2021-05-25 - version 4.4.51
* Fix   - Remove girth value for international rectangular requests.
* Fix   - Incorrect package name saved in order meta data.

2021-04-20 - version 4.4.50
* Tweak - Small update on how we batch USPS packages.

2020-11-24 - version 4.4.49
* Tweak - PHP 8 compatibility fixes.

2020-09-23 - version 4.4.48
* Tweak - Add filter for custom services rate name.
* Tweak - Update deprecated function call.
* Tweak - Remove unused services.

2020-08-19 - version 4.4.47
* Fix   - Replace deprecated jQuery methods.

2020-08-11 - version 4.4.46
* Fix - Update USPS API url.

2020-07-01 - version 4.4.45
* Fix - Dimensions not passing correctly when using "Pack into boxes" method.

2020-06-05 - version 4.4.44
* Add - Setting to control tax status.
* Tweak - Order shipping line item show quoted package name instead of product dimensions.
* Tweak - WC 4.2 compatibility.

2020-04-30 - version 4.4.43
* Tweak - WC 4.1 compatibility.

2020-04-21 - version 4.4.42
* Fix - Do not include Regional Rate boxes for Retail rates.

2020-04-14 - version 4.4.41
* Tweak - Adjust debug message, some returned rates may not be shown because of size restrictions.
* Tweak - Split API requests for regular/large items so that all rates are returned by the API.

2020-04-01 - version 4.4.40
* Tweak - Remove legacy code.
* Fix - Split API requests into batches of 25 packages when we exceed the API limit.
* Fix - Revert padded flat rate envelopes to using a rigid envelope calculation for the box packer.
* Fix - Apply letter dimension checks for Metered Letters.

2020-03-17 - version 4.4.39
* Fix - Do not display commercial services when "Retail Rates" are selected.

2020-02-27 - version 4.4.38
* Fix - Allow percentage adjustments to offer a free rate.
* Tweak - WC 4.0 compatibility.

2020-02-05 - version 4.4.37
* Fix - Use proper escape for attributes.

2020-01-16 - version 4.4.36
* Tweak - WC 3.9 compatibility.

2019-11-12 - version 4.4.35
* Tweak - Updates flat rates description in USPS settings.

2019-11-04 - version 4.4.34
* Tweak - Adds a description to the Parcel Packing Method field.
* Tweak - WC 3.8 compatibility.

2019-10-30 - version 4.4.33
* Fix - Will not return any rate if any item is greater than 70lbs.
* Fix - Remove girth for domestic rectangular request since it is miscalculated otherwise.

2019-10-15 - version 4.4.32
* Tweak - Remove outdated First-Class Mail Parcel service.

2019-09-09 - version 4.4.31
* Tweak - Removes deprecated <Size> tag from requests.
* Fix   - Missing flat rates.

2019-08-29 - version 4.4.30
* Tweak - Ensure origin zip field is never hidden.

2019-08-19 - version 4.4.29
* Tweak - Move origin zip top of form as it's required for flat rates as well.

2019-08-06 - version 4.4.28
* Tweak - WC 3.7 compatibility.

2019-07-17 - version 4.4.27
* Fix    - Flat Rate Box option available when large and small items are in the cart.

2019-06-27 - version 4.4.26
* Fix    - Flat Rate Box Pricing using incorrect rates for some countries.

2019-06-11 - version 4.4.25
* Fix    - Change WooThemes.com links to WooCommerce.com links
* Fix    - Add direct link for premium support form
* Fix    - Moves support links to under extension description

2019-06-03 - version 4.4.24
* Fix    - Re-adds First Class Package International Service.
* Update - Format debug info for better readability and only show to admin users.

2019-05-14 - version 4.4.23
* Update - Removing First Class International shipping because it has been discontinued by USPS for merchandise as of January, 2018.
* Fix    - PHP 7.3 switch warning.

2019-04-16 - version 4.4.22
* Tweak - WC 3.6 compatibility.

2019-03-26 - version 4.4.21
* Fix    - Request large envelope rates for envelopes longer than 12 inches.

2019-01-29 - version 4.4.20
* Fix    - Envelope rates returned for boxes thicker than 1inch
* Update - 2019 price changes.

2018-10-17 - version 4.4.19
* Fix    - Add i11a package.
* Update - Add letter option to individual products and declared value option for international shipping.
* Update - Create .pot file for translations.
* Update - WC 3.5 compatibility.

2018-05-22 - version 4.4.18
* Update - Privacy policy notification.
* Update - WC 3.4 compatibility.

2018-04-24 - version 4.4.17
* Fix - Priority Mail rates no longer showing.

2018-04-04 - version 4.4.16
* Fix - Enable flat rate boxes setting not honoring setting.

2018-02-15 - version 4.4.15
* Fix   - Additional updates for USPS prices per Jan 21 increase.

2018-02-09 - version 4.4.14
* Fix   - Additional updates for USPS prices per Jan 21 increase.

2018-01-29 - version 4.4.13
* Tweak - Renamed Medium and Large Flat Rate Boxes to match those on usps.com Postal Store.
* Fix   - Additional updates for USPS prices per Jan 21 increase.

2018-01-24 - version 4.4.12
* Tweak - Add filter to be used by developers for boxpacking.
* Fix   - Envelope rates returned even for boxes thicker than 1 inch.
* Fix   - Update USPS prices per Jan 21 increase.

2017-12-13 - version 4.4.11
* Fix - Added missing validation check for domestic "Package" type. Previously, it skips entirely which might causing First-Class Package Service - Retail™ not displayed on the list of available rates.
* Update - WC tested up to version.

2017-09-20 - version 4.4.10
* Add - WC minimum requirements to header.
* Fix - Added new sub service First-Class Package Service - Retail™ so that it's displayed in service settings.

2017-08-23 - version 4.4.9
* Fix - Issue where rates for American Samoa and United States Minor Outlying Islands are not showing up.
* Tweak - Don't cache API response without rates and reduce cache expiration to 1 week (filter is available to override).

2017-06-29 - version 4.4.8
* Fix - Issue where "Restrict Media Mail to..." is not checked properly.

2017-06-09 - version 4.4.7
* Tweak - Add rate meta data for flat rate boxes. This info will displayed in order line item.
* Fix - Issue where MailType is not set correctly for for First Class International rates.

2017-05-16 - version 4.4.6
* Tweak - Add package information to order details.
* Update - Priority flat rate pricing.
* Fix - Change type of additional fee to 'price' because user could enter invalid value such as 50¢.

2017-04-03 - version 4.4.5
* Update - Additional updates for WC 3.0 compatibility.

2017-02-03 - version 4.4.4
* Update - January 22, 2017 Priority Mail rates update.
* Update - WC 3.0 compatibility.
* Fix - Regional Rate A2 box dimensions were off causing wrong box type to be returned.

2016-12-22 - version 4.4.3
* Fix - Northern Mariana Islands not returning domestic rates.

2016-11-17 - version 4.4.2
* Update - International flat box rates.

2016-11-05 - version 4.4.1
* Fix - Guam rates were not calculating.
* Fix - When debug is enabled, the "error" notice type is preventing from checkout.
* New - Introduce "woocommerce_shipping_usps_request" filter.
* New - Add USPS T&C.

2016-10-07 - version 4.4.0
* Add - Support for WooCommerce 2.6+ shipping zones.
* Update - Change plugin main file name to woocommerce-shipping-usps.

2016-06-17 - version 4.3.3
* Update - Rate method name from Standard Post to Retail Ground.
* Remove - References to the deprecated type C box sizes.
* Fix - Strict standard notice caused by WooCommerce 2.6 signature change.

2016-02-29 - version 4.3.2
* Fix - First class large envelope sometimes does not return a rate.
* Fix - Prevent rates from returning if no shipping country has been selected.

2016-02-24 - version 4.3.1
* Add - Rate type setting is returned due to some 3rd party services requires this to have rate type difference.

2016-02-24 - version 4.3.0
* Fix - Fix box packer when letter type is enabled for international envelope is used.
* Fix - Weight based shipping is not returning enabled first class mail.
* Add - Additional First Class mail types.
* Add - States as countries.
* Update - Rate update for flat rate boxes for 2016.
* Remove - Shipping Rates setting as all rates are now retailed since Jan. 2016.

2016-01-26 - version 4.2.16
* Fix - Improve clarity of settings regarding Retail and Commercial shipping rates.
* Fix - Re-Add Commercial discount rates to flatrate values.

2016-01-19 - version 4.2.15
* Fix - Use correct ONLINE rates for USPS products

2016-01-15 - version 4.2.14
* Update - USPS January 2016 API Updates https://www.usps.com/business/web-tools-apis/2016-jan-webtools-release-notes.rtf
* Update - Remove deprecated USPS services (Box Rate C, Priority Mail Express Flat Rate Box)
* Update - Update flat rate shipping prices
* Fix - Resolved several PHP warnings

2015-12-11 - version 4.2.13
* Fix - Update international rates

2015-07-30 - version 4.2.12
* Tweak - Update flat rates.
* Tweak - Added back Metered Letter.
* Fix - Rate calculation for a number of international rates has been fixed.

2015-07-30 - version 4.2.11
* Fix - Calculating rates for Brunei now works correctly
* Fix - Remove use of sslverify=false in remote requests
* Added a check when saving the User ID field to make sure that the ID is valid.

2015-07-24 - version 4.2.10
* Fix - Ensure the XML response is of the correct data type before beginning parsing, now that we use WC_Safe_DOMDocument

2015-07-15 - version 4.2.9
* Fix - Error when non valid rate is returned.

2015-07-15 - version 4.2.8
* Tweak - Removed tube rates which are not really 'flat' rates.

2015-07-15 - version 4.2.7
* Fix - Sanitize XML responses.
* Fix - Added missing Priority Mail&#0174; Hold For Pickup rate.
* Tweak - More debugging notices added.

2015-07-10 - version 4.2.6
* remove international medium tube

2015-06-30 - version 4.2.5
* Corrected i99 tube price.

2015-06-19 - version 4.2.4
* Round price adjustments.

2015-04-28 - version 4.2.3
* Append svc commitment to rate names.

2015-04-21 - version 4.2.2
* Fix inccorect B2 dimension

2015-03-11 - version 4.2.1
* Fix inccorect B2 dimension
* Report boxes packed correctly.
* Option to exclude countries from USPS shipping.
* Update service names.

2015-03-03 - version 4.2.0
* Automatically define regional rate box sizes if regional rates are enabled.
* Fix regional rate dimension checks.
* Use regional rate box inner dimensions.
* Fix box ID generation.

2015-02-27 - version 4.1.10
* When there is a priority and flat rate box priority rate, return only the cheapest.

2015-01-26 - version 4.1.9
* Fix XML for international Rates

2015-01-26 - version 4.1.8
* International box shape fixes.
* Fix dimensions for priority mail flat rate medium.
* Remove depreciated functions.

2015-01-13 - version 4.1.7
* Fix international value of package.
* Fix GXG rates.

2014-12-02 - version 4.1.6
* Only set container for large packages.

2014-11-27 - version 4.1.5
* Set container to RECTANGULAR for domestic requests for more accurate rates vs the website.

2014-11-04 - version 4.1.4
* Do media mail check when products do not have dimensions.

2014-11-02 - version 4.1.3
* Fix weight based shipping when dealing with multiple split packages.

2014-10-13 - version 4.1.2
* Add additional Large Flat Rate Box
* Update box packer

2014-10-08 - version 4.1.1
* Clarify Flat Rate selections
* Update box packer

2014-09-04 - version 4.1.0
* Updated flat rate box prices.
* Updated for Sep 2014 API changes.
* Note on API: If dimensions (<Length>, <Width>, <Height>, <Girth>) are provided in the request, they will be used to filter out Flat Rate options where the packaging is smaller than the dimensions provided.

2014-07-21 - version 4.0.0
* Added medium tube rates.
* Updated textdomain.
* Refactored main file.
* Refactored rates and boxes into own files.
* New option to handle unpacked items; ignore, fallback cost, abort, or quote.
* Updated box packer to allow custom volumes for boxes.
* Updated box packer to choose suitable items for tubes (if defined).
* Updated box packer to better deal with flexible packets.
* Added international padded flat rate envelope.

2014-06-03 - version 3.7.5
* Prevent autoloading of transients by setting expirey.
* Allow transients to be used for all requests. Caused live mode to fail on certain queries.

2014-05-29 - version 3.7.4
* Added missing box name when adding box.
* Make first class filters run for weight based shipping too.

2014-05-15 - version 3.7.3
* Added filters to disable first class rates

2014-05-14 - version 3.7.2
* Tweak sizes used in checks

2014-05-12 - version 3.7.1
* Fix rate skipping checks
* Add box name for easier debugging

2014-05-01 - version 3.7.0
* Handle first-class and regional rate size restrictions due to API limitations

2014-04-24 - version 3.6.3
* Added First-Class Mail&#0174; Metered Letter
* Filter flat rate boxes

2014-03-28 - version 3.6.2
* Fix dimension check in weight_based_shipping

2014-01-16 - version 3.6.1
* Fix commercialflag for international shipping requests

2014-01-02 - version 3.6.0
* MX max weight increased to 70lbs
* Updated rates for 2014
* WC 2.1 compatibility
* Changed the way transients store data

2013-11-13 - version 3.5.1
* Force media mail to array

2013-10-29 - version 3.5.0
* Option to limit media mail to specific shipping classes

2013-09-13 - version 3.4.0
* Option to add fees to flat rate boxes
* Re-organized settings
* Fixed notices

2013-09-13 - version 3.3.2
* Fixed lbs conversion
* Include un-translated country names

2013-09-13 - version 3.3.1
* Version bump for beta testers.

2013-09-05 - version 3.3.0
* Weight based shipping option for small packages
* Added online rates for flat rate boxes
* Added priority mail express international flat rate boxes
* Rather than abort on missing dimensions, use '1' for each dimension/weight.

2013-08-22 - version 3.2.6
* Express mail is now priority mail express

2013-08-12 - version 3.2.5
* Prevented case where domestic rate names showed up for international orders.
* Added letter option to box packing to get envelope rates.

2013-08-09 - version 3.2.4
* renamed international 'flats' to Large Envelope.

2013-08-04 - version 3.2.3
* Choose between online and offline rates in admin

2013-07-23 - version 3.2.2
* Ensure code is set when getting quotes
* Fix first-class domestic rates

2013-07-14 - version 3.2.1
* Set debug default to no

2013-06-22 - version 3.2.0
* Switched to ONLINE rates
* Added Regional Rate Boxes
* Similar rates are merged + new UI for services

2013-06-21 - version 3.1.11
* Improved error handling for API downtime

2013-05-13 - version 3.1.10
* Change service names for flat rate boxes

2013-05-13 - version 3.1.9
* Added other d0 rates
* Quote 1 day in future.

2013-05-13 - version 3.1.8
* Truncate zip to 5 chars

2013-04-12 - version 3.1.7
* Flat rate priority/express optional.

2013-02-26 - version 3.1.6
* Updated USPS flat rates to new costs.

2013-02-01 - version 3.1.5
* Rename flat rate boxes

2013-02-01 - version 3.1.4
* Republic of Ireland workaround

2013-01-30 - version 3.1.3
* Show priority flat rate before express flat rate
* Parcel Post -> Standard Post

2013-01-30 - version 3.1.2
* Added flat rate medium box
* Saved the world from rogue d55 flat rate box

2013-01-29 - version 3.1.1
* Fix virtual item detection

2013-01-29 - version 3.1.0
* Broke flat rate boxes into 2 groups - express and priority. This offers 2 rates instead of one.

2012-12-04 - version 3.0.0
* Complete rewrite using USPS's latest API's
