{"name": "woocommerce/woocommerce-shipping-usps", "description": "Obtain shipping rates dynamically via the USPS Shipping API for your orders.", "homepage": "https://woocommerce.com/products/usps-shipping-method/", "type": "wordpress-plugin", "license": "GPL-2.0+", "repositories": [{"type": "vcs", "url": "https://github.com/woocommerce/box-packer"}], "archive": {"exclude": ["!/dist", "!/languages", "bin", "tests", "/vendor/dvdoug/boxpacker", "!/vendor/dvdoug/boxpacker/src", "!/vendor/dvdoug/boxpacker/features/bootstrap", "/vendor/woocommerce/box-packer", "!/vendor/woocommerce/box-packer/src", "node_modules", "README.md", "package.json", "package-lock.json", "composer.json", "composer.lock", "phpunit.xml.dist", "woocommerce-shipping-usps.zip", ".*", "pnpm-lock.yaml"]}, "require": {"php": ">=7.4", "woocommerce/box-packer": "1.2.0", "automattic/jetpack-autoloader": "^3"}, "require-dev": {"woocommerce/woocommerce-sniffs": "*", "wp-coding-standards/wpcs": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "woocommerce/qit-cli": "*", "phpstan/phpstan": "^2", "szepeviktor/phpstan-wordpress": "^2", "php-stubs/wp-cli-stubs": "*", "lucasbustamante/stubz": "^0"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "automattic/jetpack-autoloader": true}}, "scripts": {"phpstan": ["./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2"], "check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipping-usps --zip=woocommerce-shipping-usps.zip"]}}