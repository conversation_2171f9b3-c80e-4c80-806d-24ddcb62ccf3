# Attempts to match the qit runner config generated here: https://github.com/wooqit/qit-runner/blob/e3d8deae69aca9bd47911f871906831f6de63eea/ci/tests/phpstan/generate-phpstan.php
includes:
    - ../vendor/szepeviktor/phpstan-wordpress/extension.neon

parameters:
    paths:
        - ../
    excludePaths:
        analyse:
            - ../node_modules/*
            - ../vendor/*
            - ../languages/*
            - ../tests/*
            - ../.*/*
        analyseAndScan:
    scanFiles:
        - ../vendor/php-stubs/wordpress-stubs/wordpress-stubs.php
        - ../vendor/php-stubs/wp-cli-stubs/wp-cli-stubs.php
        - ../vendor/php-stubs/wp-cli-stubs/wp-cli-commands-stubs.php
        - ../vendor/php-stubs/wp-cli-stubs/wp-cli-i18n-stubs.php
        - ../vendor/php-stubs/wp-cli-stubs/wp-cli-tools-stubs.php